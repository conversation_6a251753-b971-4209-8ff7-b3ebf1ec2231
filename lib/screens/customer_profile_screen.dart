import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/platform_icon.dart';
import '../widgets/custom_text_field.dart';
import '../widgets/smart_svg_icon.dart';
import '../controllers/customer_profile_controller.dart';
import '../models/customer_profile_model.dart';
import '../models/order_model.dart';

class CustomerProfileScreen extends GetView<CustomerProfileController> {
  const CustomerProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isMobile = constraints.maxWidth <= 768;

          if (isMobile) {
            return Scaffold(
              backgroundColor: AppColors.backgroundColor,
              appBar: _buildMobileAppBar(),
              drawer: _buildMobileDrawer(),
              body: _buildMainContent(),
            );
          }

          return Row(
            children: [
              // Left Sidebar
              _buildSidebar(),

              // Main Content
              Expanded(child: _buildMainContent()),
            ],
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildMobileAppBar() {
    return AppBar(
      backgroundColor: AppColors.primaryColor,
      elevation: 0,
      leading: Builder(
        builder:
            (context) => IconButton(
              icon: PlatformIcon(
                iconName: 'menu',
                size: MySize.size24,
                color: AppColors.blackColor,
              ),
              onPressed: () => Scaffold.of(context).openDrawer(),
            ),
      ),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          SmartIcon(
            assetPath: 'assets/icons/logo_icon.svg',
            height: MySize.size80,
            width: MySize.size84,
            color: AppColors.blackColor,
          ),
        ],
      ),
      centerTitle: false,
    );
  }

  Widget _buildSidebar() {
    return Container(
      width: MySize.size200,
      color: AppColors.primaryColor,
      child: Column(
        children: [
          // Logo Section
          Container(
            padding: EdgeInsets.all(MySize.size20),
            child: Column(
              children: [
                SmartIcon(
                  assetPath: 'assets/icons/logo_icon.svg',
                  height: MySize.size70,
                  width: MySize.size129,
                  color: AppColors.blackColor,
                ),
              ],
            ),
          ),

          // Navigation Items
          Expanded(
            child: Obx(
              () => Column(
                children: [
                  _buildNavItem(
                    icon: 'home',
                    label: 'Dashboard',
                    isSelected: controller.selectedNavIndex.value == 0,
                    onTap: () => controller.selectNavItem(0),
                  ),
                  _buildNavItem(
                    icon: 'person',
                    label: 'Customers List',
                    isSelected: controller.selectedNavIndex.value == 1,
                    onTap: () => controller.selectNavItem(1),
                  ),
                  _buildNavItem(
                    icon: 'shopping_cart',
                    label: 'Orders List',
                    isSelected: controller.selectedNavIndex.value == 2,
                    onTap: () => controller.selectNavItem(2),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required String icon,
    required String label,
    required bool isSelected,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: MySize.size16,
        vertical: MySize.size4,
      ),
      decoration: BoxDecoration(
        color: isSelected ? Colors.white : Colors.transparent,
        borderRadius: BorderRadius.circular(MySize.size8),
      ),
      child: ListTile(
        leading: PlatformIcon(
          iconName: icon,
          size: MySize.size20,
          color: isSelected ? AppColors.blackColor : Colors.white,
        ),
        title: Text(
          label,
          style: TextStyle(
            fontSize: MySize.size14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected ? AppColors.blackColor : Colors.white,
          ),
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildMobileDrawer() {
    return Drawer(
      backgroundColor: AppColors.primaryColor,
      child: SafeArea(
        child: Column(
          children: [
            // Logo Section
            Container(
              padding: EdgeInsets.all(MySize.size20),
              child: Column(
                children: [
                  SmartIcon(
                    assetPath: 'assets/icons/logo_icon.svg',
                    height: MySize.size100,
                    width: MySize.size100,
                    color: AppColors.blackColor,
                  ),
                ],
              ),
            ),

            // Divider
            Divider(
              color: AppColors.blackColor.withValues(alpha: 0.2),
              thickness: 1,
              indent: MySize.size16,
              endIndent: MySize.size16,
            ),

            // Navigation Items
            Expanded(
              child: Obx(
                () => Column(
                  children: [
                    _buildMobileNavItem(
                      icon: 'home',
                      label: 'Dashboard',
                      isSelected: controller.selectedNavIndex.value == 0,
                      onTap: () {
                        Navigator.of(Get.context!).pop(); // Close drawer
                        controller.selectNavItem(0);
                      },
                    ),
                    _buildMobileNavItem(
                      icon: 'person',
                      label: 'Customers List',
                      isSelected: controller.selectedNavIndex.value == 1,
                      onTap: () {
                        Navigator.of(Get.context!).pop(); // Close drawer
                        controller.selectNavItem(1);
                      },
                    ),
                    _buildMobileNavItem(
                      icon: 'shopping_cart',
                      label: 'Orders List',
                      isSelected: controller.selectedNavIndex.value == 2,
                      onTap: () {
                        Navigator.of(Get.context!).pop(); // Close drawer
                        controller.selectNavItem(2);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileNavItem({
    required String icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: MySize.size12,
          vertical: MySize.size4,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: MySize.size16,
          vertical: MySize.size12,
        ),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(MySize.size8),
        ),
        child: Row(
          children: [
            PlatformIcon(
              iconName: icon,
              size: MySize.size20,
              color: isSelected ? AppColors.primaryColor : AppColors.blackColor,
            ),
            SizedBox(width: MySize.size12),
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color:
                      isSelected
                          ? AppColors.primaryColor
                          : AppColors.blackColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isTablet = constraints.maxWidth > 768;
        final isMobile = constraints.maxWidth <= 768;

        return Container(
          padding: EdgeInsets.all(isMobile ? MySize.size12 : MySize.size24),
          child: Obx(() {
            if (controller.isLoading.value) {
              return const Center(child: CircularProgressIndicator());
            }

            if (controller.customerProfile.value == null) {
              return const Center(child: Text('Customer profile not found'));
            }

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Top Search Bar
                if (isTablet) _buildTopSearchBar(),

                if (isTablet) SizedBox(height: MySize.size16),

                // Divider
                if (isTablet)
                  Divider(
                    color: AppColors.borderColor,
                    thickness: 1,
                    height: 1,
                  ),

                if (isTablet) SizedBox(height: MySize.size24),

                // Header and Action Bar
                _buildHeaderAndActionRow(isMobile: isMobile),

                SizedBox(height: isMobile ? MySize.size16 : MySize.size24),

                // Customer Profile Content
                Expanded(
                  child: SingleChildScrollView(
                    child:
                        isMobile ? _buildMobileLayout() : _buildDesktopLayout(),
                  ),
                ),
              ],
            );
          }),
        );
      },
    );
  }

  Widget _buildTopSearchBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        SizedBox(
          width: 300,
          child: CustomTextField(
            controller: TextEditingController(),
            hintText: 'Search Orders',
            prefixIcon: Padding(
              padding: EdgeInsets.all(MySize.size12),
              child: PlatformIcon(
                iconName: 'search',
                size: MySize.size20,
                color: AppColors.primaryColor,
              ),
            ),
            fillColor: Colors.white,
            borderColor: AppColors.borderColor,
            borderRadius: MySize.size20,
            contentPadding: EdgeInsets.symmetric(
              horizontal: MySize.size16,
              vertical: MySize.size10,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeaderAndActionRow({bool isMobile = false}) {
    if (isMobile) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Section
          Text(
            'Customer Profile',
            style: TextStyle(
              fontSize: MySize.size20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: MySize.size4),
          Text(
            'Detailed information and order history',
            style: TextStyle(
              fontSize: MySize.size12,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      );
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header Section (Left side)
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Customer Profile',
                style: TextStyle(
                  fontSize: MySize.size24,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              SizedBox(height: MySize.size4),
              Text(
                'Detailed information and order history',
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      children: [
        _buildCustomerProfileCard(),
        SizedBox(height: MySize.size16),
        _buildOrderSummaryCard(),
        SizedBox(height: MySize.size16),
        _buildOrderProgressCard(),
        SizedBox(height: MySize.size16),
        _buildRecentOrdersCard(),
        SizedBox(height: MySize.size16),
        _buildRemarksNotesCard(),
        SizedBox(height: MySize.size16),
        _buildContactLogCard(),
      ],
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left Column
        Expanded(
          flex: 1,
          child: Column(
            children: [
              _buildCustomerProfileCard(),
              SizedBox(height: MySize.size16),
              _buildOrderSummaryCard(),
            ],
          ),
        ),
        SizedBox(width: MySize.size16),
        // Right Column
        Expanded(
          flex: 1,
          child: Column(
            children: [
              _buildOrderProgressCard(),
              SizedBox(height: MySize.size16),
              _buildRecentOrdersCard(),
              SizedBox(height: MySize.size16),
              _buildRemarksNotesCard(),
              SizedBox(height: MySize.size16),
              _buildContactLogCard(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCustomerProfileCard() {
    final profile = controller.customerProfile.value!;

    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Customers Profile',
            style: TextStyle(
              fontSize: MySize.size18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: MySize.size20),

          // Profile Image in Center
          Center(
            child: Container(
              width: MySize.size100,
              height: MySize.size100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.primaryColor.withValues(alpha: 0.2),
              ),
              child: ClipOval(
                child:
                    profile.profileImagePath != null
                        ? Image.asset(
                          profile.profileImagePath!,
                          fit: BoxFit.cover,
                        )
                        : Icon(
                          Icons.person,
                          size: MySize.size50,
                          color: AppColors.primaryColor,
                        ),
              ),
            ),
          ),
          SizedBox(height: MySize.size20),

          // Customer Name (Centered)
          Center(
            child: Text(
              profile.name,
              style: TextStyle(
                fontSize: MySize.size20,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          SizedBox(height: MySize.size16),

          // Contact Details
          Column(
            children: [
              Row(
                children: [
                  // Phone
                  Row(
                    children: [
                      PlatformIcon(
                        iconName: 'phone',
                        size: MySize.size16,
                        color: AppColors.textSecondary,
                      ),
                      SizedBox(width: MySize.size8),
                      Text(
                        profile.phoneNumber,
                        style: TextStyle(
                          fontSize: MySize.size14,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),

                  SizedBox(width: MySize.size8),

                  // Email
                  Row(
                    children: [
                      PlatformIcon(
                        iconName: 'email',
                        size: MySize.size16,
                        color: AppColors.textSecondary,
                      ),
                      SizedBox(width: MySize.size8),
                      Expanded(
                        child: Text(
                          profile.email,
                          style: TextStyle(
                            fontSize: MySize.size14,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              SizedBox(height: MySize.size8),

              // Location
              Row(
                children: [
                  PlatformIcon(
                    iconName: 'location',
                    size: MySize.size16,
                    color: AppColors.textSecondary,
                  ),
                  SizedBox(width: MySize.size8),
                  Text(
                    profile.location,
                    style: TextStyle(
                      fontSize: MySize.size14,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
              SizedBox(height: MySize.size8),

              // Business
              if (profile.businessName != null)
                Row(
                  children: [
                    PlatformIcon(
                      iconName: 'building',
                      size: MySize.size16,
                      color: AppColors.textSecondary,
                    ),
                    SizedBox(width: MySize.size8),
                    Text(
                      profile.businessName!,
                      style: TextStyle(
                        fontSize: MySize.size14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummaryCard() {
    final profile = controller.customerProfile.value!;
    final mostRecentOrder =
        controller.customerOrders.isNotEmpty
            ? controller.customerOrders.first
            : null;

    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Summary',
            style: TextStyle(
              fontSize: MySize.size18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: MySize.size20),

          // Summary Stats with Card Backgrounds
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: EdgeInsets.all(MySize.size16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2196F3).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(MySize.size8),
                  ),
                  child: Column(
                    children: [
                      Text(
                        '${profile.totalOrders}',
                        style: TextStyle(
                          fontSize: MySize.size24,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF2196F3),
                        ),
                      ),
                      SizedBox(height: MySize.size4),
                      Text(
                        'Total Orders',
                        style: TextStyle(
                          fontSize: MySize.size12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(width: MySize.size12),
              Expanded(
                child: Container(
                  padding: EdgeInsets.all(MySize.size16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2196F3).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(MySize.size8),
                  ),
                  child: Column(
                    children: [
                      Text(
                        '₹${profile.totalSpent.toStringAsFixed(0)}',
                        style: TextStyle(
                          fontSize: MySize.size24,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF2196F3),
                        ),
                      ),
                      SizedBox(height: MySize.size4),
                      Text(
                        'Total Spent',
                        style: TextStyle(
                          fontSize: MySize.size12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: MySize.size12),

          Row(
            children: [
              Expanded(
                child: Container(
                  padding: EdgeInsets.all(MySize.size16),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFF9800).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(MySize.size8),
                  ),
                  child: Column(
                    children: [
                      Text(
                        '₹${profile.avgOrderValue.toStringAsFixed(0)}',
                        style: TextStyle(
                          fontSize: MySize.size20,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFFFF9800),
                        ),
                      ),
                      SizedBox(height: MySize.size4),
                      Text(
                        'Avg Order Value',
                        style: TextStyle(
                          fontSize: MySize.size12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(width: MySize.size12),
              Expanded(
                child: Container(
                  padding: EdgeInsets.all(MySize.size16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF4CAF50).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(MySize.size8),
                  ),
                  child: Column(
                    children: [
                      Text(
                        '2025-06-12',
                        style: TextStyle(
                          fontSize: MySize.size20,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF4CAF50),
                        ),
                      ),
                      SizedBox(height: MySize.size4),
                      Text(
                        'Total Spent',
                        style: TextStyle(
                          fontSize: MySize.size12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: MySize.size16),

          // Current Status (from most recent order)
          Row(
            children: [
              Text(
                'Current Status',
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: AppColors.textSecondary,
                ),
              ),
              const Spacer(),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.size12,
                  vertical: MySize.size6,
                ),
                decoration: BoxDecoration(
                  color: controller.getStatusColor(
                    mostRecentOrder?.status ?? 'Packing',
                  ),
                  borderRadius: BorderRadius.circular(MySize.size16),
                ),
                child: Text(
                  mostRecentOrder?.status ?? 'Packing',
                  style: TextStyle(
                    fontSize: MySize.size12,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrderProgressCard() {
    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Order Progress Timeline',
                style: TextStyle(
                  fontSize: MySize.size18,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.size8,
                  vertical: MySize.size4,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(MySize.size12),
                ),
                child: Text(
                  'Still Updating',
                  style: TextStyle(
                    fontSize: MySize.size10,
                    fontWeight: FontWeight.w500,
                    color: AppColors.blackColor,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: MySize.size20),

          // Horizontal Progress Timeline
          Obx(() {
            return SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children:
                    controller.orderProgressSteps.asMap().entries.map((entry) {
                      final index = entry.key;
                      final step = entry.value;
                      final isLast =
                          index == controller.orderProgressSteps.length - 1;
                      return _buildHorizontalProgressStep(step, isLast);
                    }).toList(),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildHorizontalProgressStep(OrderProgressStep step, bool isLast) {
    String iconPath = '';

    // Map step titles to icon paths (using available icons from assets/icons)
    switch (step.title.toLowerCase()) {
      case 'packing':
        iconPath = 'assets/icons/packing_icon.svg';
        break;
      case 'designing':
        iconPath = 'assets/icons/designing_icon.svg';
        break;
      case 'sorting':
        iconPath = 'assets/icons/sorting_icon.svg';
        break;
      case 'on-boarding':
        iconPath = 'assets/icons/onboarding_icon.svg';
        break;
      case 'design approved':
        iconPath = 'assets/icons/approval_icon.svg';
        break;
      case 'heating':
        iconPath = 'assets/icons/heating_icon.svg';
        break;
      default:
        iconPath = 'assets/icons/tick_icon.svg';
    }

    return Row(
      children: [
        Column(
          children: [
            // Step Icon
            Container(
              width: MySize.size40,
              height: MySize.size40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color:
                    step.isCompleted
                        ? AppColors.primaryColor
                        : step.isActive
                        ? const Color(0xFF2196F3)
                        : AppColors.borderColor.withValues(alpha: 0.3),
              ),
              child: Center(
                child:
                    step.isCompleted
                        ? SmartIcon(
                          assetPath: iconPath,
                          height: MySize.size20,
                          width: MySize.size20,
                          color: AppColors.blackColor,
                        )
                        : step.isActive
                        ? SmartIcon(
                          assetPath: iconPath,
                          height: MySize.size20,
                          width: MySize.size20,
                          color: Colors.white,
                        )
                        : SmartIcon(
                          assetPath: iconPath,
                          height: MySize.size20,
                          width: MySize.size20,
                          color: AppColors.textSecondary,
                        ),
              ),
            ),
            SizedBox(height: MySize.size8),

            // Step Title
            Text(
              step.title,
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w500,
                color:
                    step.isActive
                        ? AppColors.textPrimary
                        : AppColors.textSecondary,
              ),
            ),
            SizedBox(height: MySize.size2),

            // Step Date
            Text(
              step.date,
              style: TextStyle(
                fontSize: MySize.size10,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),

        // Connector Line
        if (!isLast)
          Container(
            width: MySize.size40,
            height: 2,
            margin: EdgeInsets.only(
              left: MySize.size8,
              right: MySize.size8,
              bottom: MySize.size40,
            ),
            color:
                step.isCompleted
                    ? AppColors.primaryColor
                    : AppColors.borderColor.withValues(alpha: 0.3),
          ),
      ],
    );
  }

  Widget _buildRecentOrdersCard() {
    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Orders',
            style: TextStyle(
              fontSize: MySize.size18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: MySize.size20),

          // Orders Table Header
          Container(
            padding: EdgeInsets.symmetric(vertical: MySize.size8),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: AppColors.borderColor.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    'ORDER ID',
                    style: TextStyle(
                      fontSize: MySize.size12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'DATE',
                    style: TextStyle(
                      fontSize: MySize.size12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'Items',
                    style: TextStyle(
                      fontSize: MySize.size12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'Qty',
                    style: TextStyle(
                      fontSize: MySize.size12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Amount',
                    style: TextStyle(
                      fontSize: MySize.size12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Status',
                    style: TextStyle(
                      fontSize: MySize.size12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'Action',
                    style: TextStyle(
                      fontSize: MySize.size12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Orders List
          Obx(() {
            return Column(
              children:
                  controller.recentOrders.map((order) {
                    return _buildOrderRow(order);
                  }).toList(),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildOrderRow(OrderModel order) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: MySize.size12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.borderColor.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              order.orderId,
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'JUNE 09 2025',
              style: TextStyle(
                fontSize: MySize.size12,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              'Art Paper',
              style: TextStyle(
                fontSize: MySize.size12,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '${order.itemCount}',
              style: TextStyle(
                fontSize: MySize.size12,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '₹ ${order.amount.toStringAsFixed(0)}',
              style: TextStyle(
                fontSize: MySize.size12,
                fontWeight: FontWeight.w500,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: MySize.size8,
                vertical: MySize.size4,
              ),
              decoration: BoxDecoration(
                color: controller.getStatusColor(order.status),
                borderRadius: BorderRadius.circular(MySize.size12),
              ),
              child: Text(
                order.status,
                style: TextStyle(
                  fontSize: MySize.size10,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Center(
              child: PlatformIcon(
                iconName: 'more_vert',
                size: MySize.size16,
                color: AppColors.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRemarksNotesCard() {
    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Remarks & Notes',
                style: TextStyle(
                  fontSize: MySize.size18,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              Obx(() {
                return GestureDetector(
                  onTap:
                      controller.isEditingNotes.value
                          ? controller.onSaveNotes
                          : controller.onEditNotes,
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: MySize.size8,
                      vertical: MySize.size4,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor,
                      borderRadius: BorderRadius.circular(MySize.size12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        PlatformIcon(
                          iconName:
                              controller.isEditingNotes.value ? 'save' : 'edit',
                          size: MySize.size12,
                          color: AppColors.blackColor,
                        ),
                        SizedBox(width: MySize.size4),
                        Text(
                          controller.isEditingNotes.value ? 'Save' : 'Edit',
                          style: TextStyle(
                            fontSize: MySize.size10,
                            fontWeight: FontWeight.w500,
                            color: AppColors.blackColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }),
            ],
          ),
          SizedBox(height: MySize.size16),

          Obx(() {
            if (controller.isEditingNotes.value) {
              return Column(
                children: [
                  TextField(
                    controller: controller.notesController,
                    maxLines: 4,
                    decoration: InputDecoration(
                      hintText: 'Enter notes...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(MySize.size8),
                        borderSide: BorderSide(color: AppColors.borderColor),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(MySize.size8),
                        borderSide: BorderSide(color: AppColors.primaryColor),
                      ),
                    ),
                  ),
                  SizedBox(height: MySize.size12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: controller.onCancelEditNotes,
                        child: Text(
                          'Cancel',
                          style: TextStyle(color: AppColors.textSecondary),
                        ),
                      ),
                      SizedBox(width: MySize.size8),
                      ElevatedButton(
                        onPressed: controller.onSaveNotes,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryColor,
                          foregroundColor: AppColors.blackColor,
                        ),
                        child: const Text('Save'),
                      ),
                    ],
                  ),
                ],
              );
            } else {
              return Text(
                controller.notes.value.isEmpty
                    ? 'No notes available. Click Edit to add notes.'
                    : controller.notes.value,
                style: TextStyle(
                  fontSize: MySize.size14,
                  color:
                      controller.notes.value.isEmpty
                          ? AppColors.textSecondary
                          : AppColors.textPrimary,
                ),
              );
            }
          }),
        ],
      ),
    );
  }

  Widget _buildContactLogCard() {
    final profile = controller.customerProfile.value!;

    return Container(
      padding: EdgeInsets.all(MySize.size20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Contact Log',
                style: TextStyle(
                  fontSize: MySize.size18,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              GestureDetector(
                onTap: controller.onAddContactLog,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: MySize.size8,
                    vertical: MySize.size4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primaryColor,
                    borderRadius: BorderRadius.circular(MySize.size12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      PlatformIcon(
                        iconName: 'add',
                        size: MySize.size12,
                        color: AppColors.blackColor,
                      ),
                      SizedBox(width: MySize.size4),
                      Text(
                        'Add New',
                        style: TextStyle(
                          fontSize: MySize.size10,
                          fontWeight: FontWeight.w500,
                          color: AppColors.blackColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: MySize.size16),

          // Contact Log Entries
          Column(
            children:
                profile.contactLog.map((entry) {
                  return _buildContactLogEntry(entry);
                }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildContactLogEntry(ContactLogEntry entry) {
    IconData iconData;
    Color iconColor;

    switch (entry.type) {
      case 'call':
        iconData = Icons.phone;
        iconColor = const Color(0xFF2196F3);
        break;
      case 'email':
        iconData = Icons.email;
        iconColor = const Color(0xFF4CAF50);
        break;
      case 'message':
        iconData = Icons.message;
        iconColor = const Color(0xFF9C27B0);
        break;
      default:
        iconData = Icons.contact_phone;
        iconColor = AppColors.textSecondary;
    }

    return Container(
      margin: EdgeInsets.only(bottom: MySize.size12),
      child: Row(
        children: [
          Container(
            width: MySize.size32,
            height: MySize.size32,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: iconColor.withValues(alpha: 0.1),
            ),
            child: Icon(iconData, size: MySize.size16, color: iconColor),
          ),
          SizedBox(width: MySize.size12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${entry.type.toUpperCase()} ${_formatDateTime(entry.timestamp)}',
                  style: TextStyle(
                    fontSize: MySize.size14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.textPrimary,
                  ),
                ),
                if (entry.description != null) ...[
                  SizedBox(height: MySize.size2),
                  Text(
                    entry.description!,
                    style: TextStyle(
                      fontSize: MySize.size12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} at ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')} ${dateTime.hour >= 12 ? 'PM' : 'AM'}';
  }
}
